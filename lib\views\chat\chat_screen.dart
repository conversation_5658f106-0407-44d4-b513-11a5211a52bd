import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/socket/socket_service.dart';
import 'package:room_eight/viewmodels/chat_bloc/chat_bloc.dart';
import 'package:room_eight/widgets/custom_widget/custom_debounce.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/loading_animation_widget.dart';
import 'package:room_eight/core/utils/string_extension.dart';
import 'package:room_eight/models/chat_model/chat_message_list_model.dart';
import 'package:room_eight/views/chat/widget/date_time_utils.dart';
import 'package:room_eight/views/chat/widget/link_preview.dart';
import 'package:room_eight/views/chat/widget/typing_indicator_widget.dart';
import 'package:room_eight/widgets/common_widget/app_alert_dialog.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';

enum TypeWriterStatus { typing, typed }

class ChatScreen extends StatefulWidget {
  final dynamic args;

  const ChatScreen({super.key, this.args});
  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    return ChatScreen(args: args);
    // BlocProvider<ChatBloc>(
    //   create: (context) =>
    //       ChatBloc(const ChatState(), ChatRepository(apiClient: ApiClient()))
    //         ..add(ChatInitialEvent()),
    //   child: ChatScreen(args: args),
    // );
  }

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  ScrollController? _scrollController;
  bool _showScrollToBottomButton = false;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  ValueNotifier<TypeWriterStatus> composingStatus = ValueNotifier(
    TypeWriterStatus.typed,
  );
  final ImagePicker _imagePicker = ImagePicker();
  // RecorderController? controller;
  ValueNotifier<bool> isRecording = ValueNotifier(false);
  late Debouncer debouncer;
  late Debouncer typingDebouncer;
  final ValueNotifier<bool> _showTypingIndicator = ValueNotifier(false);
  ValueListenable<bool> get typingIndicatorNotifier => _showTypingIndicator;
  bool _isCurrentlyTyping = false;
  int lastMassageID = 0;

  // bool _isStart = true;

  @override
  void initState() {
    // Debug widget args
    Logger.lOG("ChatScreen widget.args: ${widget.args}");
    Logger.lOG("ChatScreen widget.args[0]: ${widget.args[0]}");
    Logger.lOG("ChatScreen widget.args[0].userId: ${widget.args[0].userId}");

    SocketService.emit(SocketConfig.joinSocket, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
    });
    SocketService.response(SocketConfig.joinSocket, (joinSocket) {
      Logger.lOG(joinSocket);
    });
    debouncer = Debouncer(const Duration(seconds: 1));
    typingDebouncer = Debouncer(const Duration(seconds: 2));
    super.initState();
    _scrollController = ScrollController()..addListener(_scrollListener);

    scheduleMicrotask(
      () => context.read<ChatBloc>().add(
        GetChatMessageListEvent(
          page: 1,
          userId: (widget.args[0].userId ?? 0).toString(),
        ),
      ),
    );
    if (context.read<ChatBloc>().state.chatMessageList.isNotEmpty) {
      context.read<ChatBloc>().state.chatMessageList.clear();
    }

    if (defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.android) {
      // controller = RecorderController();
    }
    _scrollController?.addListener(() {
      if (_scrollController!.offset > 300) {
        // If the user scrolls up 300px
        setState(() {
          _showScrollToBottomButton = true;
        });
      } else {
        setState(() {
          _showScrollToBottomButton = false;
        });
      }
    });

    // Setup socket listeners
    _setupSocketListeners();
  }

  void _setupSocketListeners() {
    // Typing indicator listener
    SocketService.response(SocketConfig.isTyping, (response) {
      if (response != null && response['is_typing'] != null) {
        // Only show typing indicator if it's from the other user (not self)
        final fromUserId =
            response['user_id'] ?? 0; // Changed from 'from' to 'user_id'
        final currentUserId =
            Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;

        Logger.lOG("From user: $fromUserId, Current user: $currentUserId");

        if (fromUserId != currentUserId) {
          // Handle both boolean and string/int values
          final isTyping = response['is_typing'];
          final shouldShow =
              isTyping == true ||
              isTyping == 1 ||
              isTyping == "1" ||
              isTyping == "true";

          _showTypingIndicator.value = shouldShow;
        }
      }
    });
  }

  @override
  void dispose() {
    // Stop typing when leaving the chat
    _stopTyping();
    _scrollController?.dispose();
    debouncer.dispose();
    typingDebouncer.dispose();
    // _isStart = false;
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController?.position.pixels ==
        _scrollController?.position.maxScrollExtent) {
      final state = context.read<ChatBloc>().state;
      if (!state.isLoadingMore) {
        context.read<ChatBloc>().add(
          GetChatMessageListEvent(
            page: state.page + 1,
            userId: widget.args.userId.toString(),
          ),
        );
      }
    }
  }

  void _startTyping() {
    if (!_isCurrentlyTyping) {
      _isCurrentlyTyping = true;
      context.read<ChatBloc>().add(
        TypingSocketEvent(userId: widget.args[0].userId, isTyping: "1"),
      );
    }

    // Reset the debouncer to stop typing after 2 seconds of inactivity
    typingDebouncer.run(
      () {
        // This runs after the debounce duration (stop typing)
        _stopTyping();
      },
      () {
        // This runs if the debouncer is reset before the duration completes
        // We don't need to do anything here
      },
    );
  }

  void _stopTyping() {
    if (_isCurrentlyTyping) {
      _isCurrentlyTyping = false;
      context.read<ChatBloc>().add(
        TypingSocketEvent(userId: widget.args[0].userId, isTyping: "0"),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    isMessageScreen = true;
    SocketService.response(SocketConfig.receivemessage, (response) {
      if (mounted) {
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];
        context.read<ChatBloc>().add(
          UpdateChatMessageSocketEvent(
            id: id,
            createdat: createdat,
            message: message,
            sentby: sentby,
            type: type,
          ),
        );

        // if (_isStart) {
        //   Future.delayed(Duration(seconds: 2), () {
        //     if (sentby !=
        //         (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)) {
        //       Logger.lOG("touser $touser");
        //       SocketService.emit(SocketConfig.messageRead, {
        //         'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        //         'to': Prefobj.preferences?.get(Prefkeys.USER_ID),
        //         'message_id': lastMassageID,
        //       });
        //       // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
        //     }
        //   });
        //   _isStart = false;
        // }
        setState(() {});
      }
    });

    SocketService.response(SocketConfig.sendmessage, (response) {
      if (mounted) {
        Logger.lOG("Send message response: $response");

        // Check if response has error
        if (response['status'] == false || response['error'] != null) {
          Logger.lOG("Send message error: ${response['error']}");
          // Handle error case - maybe show a snackbar or retry
          return;
        }

        // Handle success case
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];

        // Only proceed if we have valid data
        if (id != null && message != null && sentby != null) {
          context.read<ChatBloc>().add(
            UpdateChatMessageSocketEvent(
              id: id,
              createdat: createdat,
              message: message,
              sentby: sentby,
              type: type,
            ),
          );
        } else {
          Logger.lOG(
            "Invalid response data: id=$id, message=$message, sentby=$sentby",
          );
        }
        setState(() {});
      }
    });

    SocketService.response(SocketConfig.deleteMessage, (response) {
      if (mounted) {
        final messageId = response['message_id'];
        final success = response['status'] ?? false;

        if (success) {
          context.read<ChatBloc>().add(
            RemoveDeletedMessageEvent(messageId: messageId),
          );
        }
      }
    });

    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, themeState) {
            // only first time call this socket

            Future.delayed(Duration(seconds: 2), () {
              if (touser ==
                  (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)) {
                Logger.lOG("touser $touser");
                // SocketService.emit(SocketConfig.messageRead, {
                //   'Authorization': Prefobj.preferences?.get(
                //     Prefkeys.AUTHTOKEN,
                //   ),
                //   'to': touser,
                //   'message_id': state.chatMessageList.first.id,
                // });
                // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
              }
            });
            return Scaffold(
              backgroundColor: Theme.of(context).customColors.scaffoldColor,
              appBar: _buildchatAppBar(context, themeState, widget.args[0]),
              body: Stack(
                alignment: Alignment.bottomCenter / 1.3.h,
                children: [
                  Column(
                    children: [
                      // _buildchatAppBar(context, themeState, widget.args[0]),
                      Expanded(
                        child: InkWell(
                          focusColor: Colors.transparent,
                          onTap: () {
                            FocusManager.instance.primaryFocus?.unfocus();
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 20.0.w),
                            child: Column(
                              children: [
                                Visibility(
                                  visible: state.isLoadingMore,
                                  child: SizedBox(
                                    height: 50.h,
                                    child: Center(
                                      child: CupertinoActivityIndicator(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.primary,
                                      ),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: state.isloding
                                      ? const LoadingAnimationWidget()
                                      : _buildMessageList(
                                          state.chatMessageList,
                                          themeState,
                                        ),
                                ),
                                buildSizedBoxH(8.0),
                                ValueListenableBuilder(
                                  valueListenable: typingIndicatorNotifier,
                                  builder: (context, value, child) {
                                    return TypingIndicator(
                                      showIndicator: value,
                                      userrname: widget.args[0].userName
                                          .toString(),
                                      // bubblecolor: Theme.of(
                                      //   context,
                                      // ).primaryColor,
                                    );
                                  },
                                ),

                                // Debug button to test typing indicator
                                _buildMessageTextField(state, themeState),
                                Platform.isIOS
                                    ? buildSizedBoxH(30.0)
                                    : buildSizedBoxH(20.0),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (_showScrollToBottomButton)
                    FloatingActionButton(
                      mini: true,
                      backgroundColor: Theme.of(context).primaryColor,
                      onPressed: () {
                        _scrollToBottom();
                      },
                      child: const Icon(Icons.arrow_downward),
                    ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  RoomEightAppBar _buildchatAppBar(
    BuildContext context,
    ThemeState themeState,
    dynamic args,
  ) {
    return RoomEightAppBar(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      showBackButton: true,
      useGradientLeading: true,

      // title: Lang.of(context).lbl_profile,
      titleWidget: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          ClipRRect(
            clipBehavior: Clip.hardEdge,
            child: Padding(
              padding: const EdgeInsets.all(4.0),
              child: CustomImageView(
                radius: BorderRadius.circular(25.r),
                height: 40.0.h,
                width: 40.0.w,
                fit: BoxFit.cover,
                imagePath:
                    args?.profileImage.toString() == "" ||
                        args?.profileImage.toString() == null
                    ? Assets.images.pngs.other.pngAppLogo.path
                    : "${SocketConfig.mainbaseURL}${args?.profileImage.toString()}",
                alignment: Alignment.center,
              ),
            ),
          ),
          buildSizedboxW(8),
          Expanded(
            child: Text(
              args.userName.toString(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w700,
                fontSize: 16.0.sp,
              ),
            ),
          ),
        ],
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: Container(
            margin: const EdgeInsets.only(left: 16.0),
            height: 36.h,
            width: 36.w,
            child: CustomGradientContainer(
              height: 36.h,
              width: 36.w,
              topColor: Theme.of(context).customColors.fillColor!,
              bottomColor: Theme.of(context).customColors.fillColor!,
              fillColor: Theme.of(context).customColors.fillColor!,
              child: CustomImageView(
                imagePath: Assets.images.svgs.icons.icMore.path,
                onTap: () {},
              ),
            ),
          ),
        ),
      ],
      centerTitle: true,
      ontap: () {
        context.read<NavBloc>().add(const NavTabChanged(0));
      },
    );
  }
  // PreferredSizeWidget _buildchatAppBar(
  //   BuildContext context,
  //   ThemeState themeState,
  //   dynamic args,
  // ) {
  //   return AppBar(
  //     backgroundColor: Colors.transparent,
  //     surfaceTintColor: Colors.transparent,
  //     automaticallyImplyLeading: false,
  //     leadingWidth: 30.0.w,
  //     leading: CustomImageView(
  //       onTap: () {
  //         NavigatorService.goBack();
  //       },
  //       imagePath: Assets.images.svgs.icons.icBackArrow.path,
  //       height: 16.0.h,
  //       margin: EdgeInsets.only(top: 19.0.h, bottom: 19.0.w, left: 10.0.w),
  //     ),
  //     centerTitle: false,
  //     title: InkWell(
  //       onTap: () {
  //         // PersistentNavBarNavigator.pushNewScreen(context,
  //         //     screen: BlocProvider<UserProfileIdBloc>(
  //         //       create: (context) => UserProfileIdBloc(
  //         //         const UserProfileIdState(),
  //         //         ProfileRepository(apiClient: ApiClient()),
  //         //       )..add(UserProfileIdInitial()),
  //         //       child: UserProfileIdScreen(
  //         //           userId: widget.args[0].userId.toString(),
  //         //           stackonScreen: true),
  //         //     ));
  //       },
  //       child: Row(
  //         mainAxisSize: MainAxisSize.min,
  //         children: [
  //           ClipRRect(
  //             clipBehavior: Clip.hardEdge,
  //             child: Padding(
  //               padding: const EdgeInsets.all(4.0),
  //               child: CustomImageView(
  //                 radius: BorderRadius.circular(25.r),
  //                 height: 40.0.h,
  //                 width: 40.0.w,
  //                 fit: BoxFit.cover,
  //                 imagePath:
  //                     args?.profileImage.toString() == "" ||
  //                         args?.profileImage.toString() == null
  //                     ? Assets.images.pngs.other.pngAppLogo.path
  //                     : "${SocketConfig.mainbaseURL}${args?.profileImage.toString()}",
  //                 alignment: Alignment.center,
  //               ),
  //             ),
  //           ),
  //           buildSizedboxW(8),
  //           Column(
  //             mainAxisAlignment: MainAxisAlignment.start,
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 args.userName.toString(),
  //                 style: Theme.of(context).textTheme.bodyMedium?.copyWith(
  //                   fontWeight: FontWeight.w700,
  //                   fontSize: 16.0.sp,
  //                 ),
  //               ),
  //             ],
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  Widget _buildMessageList(
    List<ChatMessageData> messages,
    ThemeState themeState,
  ) {
    final groupedMessages = _groupMessagesByDate(messages);

    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      itemCount: groupedMessages.length,
      reverse: true,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final date = groupedMessages.keys.elementAt(index);
        final messages = groupedMessages[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    date.toString(),
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(fontSize: 11.0.sp),
                  ),
                ),
              ],
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              reverse: true,
              itemCount: messages.length,
              itemBuilder: (context, messageIndex) {
                lastMassageID = messages.first.id ?? 0;
                return _buildChatBubble(messages[messageIndex], themeState);
              },
            ),
          ],
        );
      },
    );
  }

  void _scrollToBottom() {
    _scrollController?.animateTo(
      0.0, // Scroll to the top as the list is reversed
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildChatBubble(ChatMessageData message, ThemeState themeState) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment:
            message.sentBy ==
                (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          Visibility(
            visible:
                message.sentBy !=
                (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?),
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(45.r),
                  border: Border.all(
                    color: Theme.of(context).customColors.primaryColor!,
                  ),
                  height: 36.0.h,
                  width: 36.0.w,
                  fit: BoxFit.cover,
                  imagePath:
                      widget.args[0]?.profileImage == null ||
                          widget.args[0]?.profileImage == ''
                      ? Assets.images.pngs.other.pngAppLogo.path
                      : "${SocketConfig.mainbaseURL}${widget.args[0]?.profileImage.toString()}",
                  alignment: Alignment.center,
                ),
              ),
            ),
          ),
          GestureDetector(
            onLongPress: () {
              _showDeleteMessageDialog(message);
            },
            onTap: () {
              // Optional: Show a hint that long press deletes message for own messages
              final currentUserId =
                  Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;
              if (message.sentBy == currentUserId) {
                // Could show a subtle hint here if needed
              }
            },
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 280.w),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: message.type == 'image' ? 0.w : 12.w,
                  vertical: message.type == 'image' ? 0.h : 10.h,
                ),
                margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
                decoration: BoxDecoration(
                  color:
                      message.sentBy ==
                          (Prefobj.preferences?.get(Prefkeys.USER_ID) as int?)
                      ? Theme.of(context).primaryColor.withValues(alpha: 0.2)
                      : ThemeData().customColors.primaryColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(
                      message.sentBy ==
                              (Prefobj.preferences?.get(Prefkeys.USER_ID)
                                  as int?)
                          ? 15.r
                          : 0.r,
                    ),
                    topRight: Radius.circular(15.r),
                    bottomRight: Radius.circular(
                      message.sentBy ==
                              (Prefobj.preferences?.get(Prefkeys.USER_ID)
                                  as int?)
                          ? 0.r
                          : 15.r,
                    ),
                    bottomLeft: Radius.circular(15.r),
                  ),
                ),
                child: message.type == 'image'
                    ? InkWell(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                          // PersistentNavBarNavigator.pushNewScreen(context,
                          //     screen: FlowkarImagePreview(
                          //       imagepath:
                          //           '${APIEndPoints.mainbaseURL}/${message.message}',
                          //     ));
                        },
                        child: CustomImageView(
                          imagePath:
                              '${SocketConfig.mainbaseURL}/${message.message}',
                          height: 250.0.h,
                          width: 150.0.w,
                          radius: BorderRadius.circular(4.0.r),
                          fit: BoxFit.cover,
                        ),
                      )
                    : message.type == 'custom'
                    ? SizedBox.shrink()
                    : message.message!.isUrl
                    ? LinkPreview(url: message.message ?? '')
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            message.message ?? '',
                            textAlign: TextAlign.start,
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color:
                                      message.sentBy ==
                                          (Prefobj.preferences?.get(
                                                Prefkeys.USER_ID,
                                              )
                                              as int?)
                                      ? ThemeData().customColors.blackColor
                                      : ThemeData().customColors.fillColor,
                                  fontSize: 16.0.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),

                          // Show pending indicator for sent messages that are still pending
                        ],
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, List<ChatMessageData>> _groupMessagesByDate(
    List<ChatMessageData> messages,
  ) {
    final Map<String, List<ChatMessageData>> groupedMessages = {};
    for (var message in messages) {
      final dateTime = DateTime.parse(
        message.createdAt.toString().split("+").first,
      );
      final formattedDate = dateTime.formatForChatMessage();
      groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
    }
    return groupedMessages;
  }

  Widget _buildMessageTextField(ChatState state, ThemeState themestate) {
    return ValueListenableBuilder<bool>(
      valueListenable: isRecording,
      builder: (_, isRecordingValue, child) {
        return Row(
          children: [
            // if (isRecordingValue && controller != null && !kIsWeb)
            //   Expanded(
            //     child: Container(
            //       decoration: BoxDecoration(
            //           color: AppColors.primaryColor,
            //           borderRadius: BorderRadius.circular(22.0.r)),
            //       child: Padding(
            //         padding: const EdgeInsets.all(4.0),
            //         child: Row(
            //           mainAxisSize: MainAxisSize.min,
            //           children: [
            //             if (isRecordingValue)
            //               CustomImageView(
            //                 height: 55.0.h,
            //                 imagePath:
            //                     Assets.images.icons.icDeleteRecording.path,
            //                 onTap: () {
            //                   _cancelRecording();
            //                 },
            //               ),
            //             Expanded(
            //               child: AudioWaveforms(
            //                 size: const Size(double.infinity, 50),
            //                 recorderController: controller!,
            //                 padding: EdgeInsets.symmetric(horizontal: 8.0.w),
            //                 decoration: BoxDecoration(
            //                     borderRadius: BorderRadius.circular(12.0.r)),
            //                 waveStyle: const WaveStyle(
            //                   extendWaveform: true,
            //                   showMiddleLine: false,
            //                   waveCap: StrokeCap.round,
            //                   waveColor: AppColors.whitecolor,
            //                 ),
            //               ),
            //             ),
            //             Padding(
            //               padding: const EdgeInsets.only(right: 8.0),
            //               child: InkWell(
            //                 onTap: _recordOrStop,
            //                 child: Padding(
            //                   padding: const EdgeInsets.all(8.0),
            //                   child: Text(
            //                     Lang.of(context).lbl_send,
            //                     style: Theme.of(context)
            //                         .textTheme
            //                         .bodySmall
            //                         ?.copyWith(
            //                             color: AppColors.whitecolor,
            //                             fontWeight: FontWeight.bold),
            //                   ),
            //                 ),
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            Visibility(
              child: Expanded(
                child: SizedBox(
                  height: 59,
                  child: BlocBuilder<ChatBloc, ChatState>(
                    builder: (context, states) {
                      TextEditingController chatController =
                          TextEditingController();
                      return CustomTextInputField(
                        type: InputType.text,
                        controller: chatController,
                        // controller: states.chatController,
                        hintLabel: 'Enter Message',
                        context: context,
                        onChanged: (inputText) {
                          _inputText.value = inputText;
                          if (inputText.isNotEmpty) {
                            _startTyping();
                          } else {
                            _stopTyping();
                          }
                        },
                        prefixIcon: ValueListenableBuilder<String>(
                          valueListenable: _inputText,
                          builder: (_, inputTextValue, __) {
                            if (inputTextValue.isEmpty) {
                              return Padding(
                                padding: const EdgeInsets.all(6.0),
                                child: CustomImageView(
                                  height: 25.h,
                                  width: 25.w,
                                  imagePath:
                                      Assets.images.pngs.icons.icCamera.path,
                                  onTap: () =>
                                      _onIconPressed(ImageSource.camera),
                                ),
                              );
                            } else {
                              return SizedBox.shrink();
                            }
                          },
                        ),
                        suffixIcon: ValueListenableBuilder<String>(
                          valueListenable: _inputText,
                          builder: (_, inputTextValue, __) {
                            if (inputTextValue.isNotEmpty) {
                              return Padding(
                                padding: EdgeInsets.only(right: 0.0.w),
                                child: InkWell(
                                  onTap: () {
                                    // Stop typing when sending message
                                    _stopTyping();

                                    context.read<ChatBloc>().add(
                                      SendMessageEvent(
                                        message: inputTextValue,
                                        file: '',
                                        touserId:
                                            int.tryParse(
                                              widget.args[0].userId.toString(),
                                            ) ??
                                            0,
                                        type: 'text',
                                      ),
                                    );
                                    setState(() {});
                                    chatController.clear();
                                    _inputText.value = '';
                                    FocusScope.of(
                                      context,
                                    ).requestFocus(FocusNode());
                                  },
                                  child: Icon(
                                    Icons.send_rounded,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                  // Text(
                                  //   // "Lang.of(context).lbl_send",
                                  //   style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
                                  // ),
                                ),
                              );
                            } else {
                              // return SizedBox.shrink();
                              return Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CustomImageView(
                                      imagePath: Assets
                                          .images
                                          .pngs
                                          .icons
                                          .icCamera
                                          .path,

                                      onTap: () =>
                                          _onIconPressed(ImageSource.gallery),
                                    ),
                                    // Padding(
                                    //   padding: EdgeInsets.only(
                                    //       top: 15.0.h, bottom: 15.0.h, right: 15.0.w),
                                    //   child: CustomImageView(
                                    //       height: 25.h,
                                    //       width: 25.0.w,
                                    //       imagePath:
                                    //           Assets.images.icons.icMicrophone.path,
                                    //       onTap: () => _recordOrStop()),
                                    // ),
                                  ],
                                ),
                              );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteMessageDialog(ChatMessageData message) {
    // Only allow deleting own messages
    final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID) as int?;
    if (message.sentBy != currentUserId) {
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAlertDialog(
          imagePath: Assets.images.svgs.icons.icDelete.path,
          title: 'Delete Message',
          subtitle:
              'Are you sure you want to delete this message? This action cannot be undone.',
          confirmButtonText: 'Delete',
          cancelButtonText: 'Cancel',
          isLoading: false,
          onConfirmButtonPressed: () {
            Navigator.pop(dialogContext);
            _deleteMessage(message);
          },
          onCancelButtonPressed: () {
            Navigator.pop(dialogContext);
          },
        );
      },
    );
  }

  void _deleteMessage(ChatMessageData message) {
    if (message.id != null) {
      final toUserId = int.tryParse(widget.args[0].userId.toString()) ?? 0;
      context.read<ChatBloc>().add(
        DeleteMessageEvent(messageId: message.id!, toUserId: toUserId),
      );

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Message deleted successfully'),
          backgroundColor: Theme.of(context).primaryColor,
        ),
      );
    }
  }

  // FutureOr<void> _cancelRecording() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) return;
  //   // final path = await controller?.stop();
  //   if (path == null) {
  //     isRecording.value = false;
  //     return;
  //   }
  //   final file = File(path);

  //   if (await file.exists()) {
  //     await file.delete();
  //   }

  //   isRecording.value = false;
  // }

  // Future<void> _recordOrStop() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) {
  //     await controller?.record(
  //       androidEncoder: AndroidEncoder.aac,
  //       iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
  //       androidOutputFormat: AndroidOutputFormat.mpeg4,
  //       bitRate: 128000,
  //       sampleRate: 44100,
  //     );
  //     isRecording.value = true;
  //   } else {
  //     final path = await controller?.stop();
  //     isRecording.value = false;
  //     path;
  //     Logger.lOG(path);
  //     // Check if an image is selected
  //     if (path != null && path.isNotEmpty) {
  //       // Get the MIME type of the image file
  //       String? mimeType = lookupMimeType(path);

  //       if (mimeType != null) {
  //         // Convert the image file to Base64
  //         File audioFile = File(path);
  //         List<int> audioBytes = await audioFile.readAsBytes();
  //         String base64audio = base64Encode(audioBytes);

  //         // Create the Data URI string
  //         String dataUri = 'data:audio/m4a;;base64,$base64audio';

  //         // Send the Data URI as a chat message

  //         context.read<ChatBloc>().add(SendMessageEvent(
  //               file: dataUri,
  //               message: "",
  //               touserId: int.tryParse(widget.args[0].userId.toString()) ?? 0,
  //               type: 'custom', // or any other type indicating an image
  //             ));
  //       } else {
  //         Logger.lOG('Could not determine MIME type.');
  //       }
  //     }
  //   }
  // }

  void _onIconPressed(ImageSource imageSource) async {
    try {
      // Pick an image from the specified image source
      final XFile? image = await _imagePicker.pickImage(
        source: imageSource,
        preferredCameraDevice: CameraDevice.rear,
      );
      if (image != null) {
        NavigatorService.pushNamed(
          AppRoutes.chatImagePreview,
          arguments: [image, widget.args[0].userId.toString()],
        );
      }
    } catch (e) {
      Logger.lOG('Error picking/sending image: ${e.toString()}');
    }
  }
}
