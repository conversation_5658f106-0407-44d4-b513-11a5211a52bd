import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/api_config/endpoints/api_endpoint.dart';
import 'package:room_eight/viewmodels/profile_bloc/profile_bloc.dart';
import 'package:room_eight/views/profile_view/widgets/birthday_picker_dialog.dart';
import 'package:room_eight/views/profile_view/widgets/gender_picker_dialog.dart';
import 'package:room_eight/views/profile_view/widgets/profile_shimmer.dart';
import 'package:room_eight/widgets/custom_widget/custom_gradient_container.dart';
import 'package:room_eight/core/utils/image_picker_utils.dart';
import 'package:room_eight/widgets/location_search_bottom_sheet.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:room_eight/core/utils/constants.dart';
import 'package:room_eight/models/profile_model/profile_model.dart';

class EditProfileScreen extends StatefulWidget {
  static Widget builder(BuildContext context) => const EditProfileScreen();

  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Backup current state before allowing edits
      context.read<ProfileBloc>().add(BackupProfileState());
      context.read<ProfileBloc>().add(GetSelectionOption());
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // Restore original state when going back without saving
          context.read<ProfileBloc>().add(RestoreProfileState());
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).customColors.scaffoldColor,
        appBar: _buildAppbar(context),
        body: BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, state) {
            if (state.isGetUserProfileLoading) {
              return const ProfileScreenShimmer();
            }
            return AbsorbPointer(
              absorbing: state.isEditProfileLoading,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [_buildFormFields(context, state)],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  RoomEightAppBar _buildAppbar(BuildContext context) {
    return RoomEightAppBar(
      backgroundColor: Theme.of(context).customColors.scaffoldColor,
      showBackButton: true,
      useGradientLeading: true,
      title: Lang.of(context).lbl_edit_profile,
      centerTitle: true,
      ontap: () {
        NavigatorService.goBack();
      },
      actions: [
        BlocBuilder<ProfileBloc, ProfileState>(
          builder: (context, state) {
            return CustomElevatedButton(
              isLoading: state.isEditProfileLoading,
              onPressed: state.isEditProfileLoading
                  ? null
                  : () {
                      context.read<ProfileBloc>().add(EditProfile());
                    },
              text: 'Save',
              height: 36.h,
              margin: EdgeInsets.only(right: 16.w),
              borderRadius: 10,
              width: 80.w,
            );
          },
        ),
      ],
    );
  }

  Widget _buildFormFields(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildSizedBoxH(16.h),
        _buildImageUpload(context, state),
        _buildNameAndEmail(context, state),
        buildSizedBoxH(24.h),
        _buildFullNameField(context, state),
        buildSizedBoxH(16.h),
        // _buildDateOfBirthField(context, state),
        _buildGenderAndAgeRow(context, state),
        // buildSizedBoxH(16.h),
        // _buildGenderField(context, state),
        buildSizedBoxH(16.h),
        _buildPreferredGenderField(context, state),
        buildSizedBoxH(16.h),
        _buildSmokingPreferenceField(context, state),
        buildSizedBoxH(16.h),
        _buildCleanlinessField(context, state),
        buildSizedBoxH(16.h),
        _buildPetPreferenceField(context, state),
        buildSizedBoxH(16.h),
        _buildClassStandingField(context, state),
        buildSizedBoxH(16.h),
        _buildPersonalityContainer(context, state),
        buildSizedBoxH(16.h),
        _buildHabitsLifestyleField(context, state),
        buildSizedBoxH(16.h),
        _buildLivingStyleField(context, state),
        buildSizedBoxH(16.h),
        _buildInterestsHobbiesField(context, state),
        buildSizedBoxH(16.h),
        _buildContactNumberField(context, state),
        buildSizedBoxH(16.h),
        _buildLeasePeriodField(context, state),
        buildSizedBoxH(16.h),
        _buildAboutField(context, state),
        buildSizedBoxH(16.h),
        _buildPreferredLocationsField(context, state),
        buildSizedBoxH(16.h),
        _buildUploadMultiplePhotos(context, state),
        buildSizedBoxH(24.h),
      ],
    );
  }

  Widget _buildNameAndEmail(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Center(
        child: Column(
          children: [
            Text(
              state.nameController.text,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: Theme.of(context).customColors.blackColor,
                fontWeight: FontWeight.bold,
                fontSize: 20.sp,
              ),
            ),
            buildSizedBoxH(4.h),
            Text(
              Prefobj.preferences!
                      .get(Prefkeys.USER_MAIL_ID)
                      .toString()
                      .isNotEmpty
                  ? Prefobj.preferences?.get(Prefkeys.USER_MAIL_ID)
                  : Lang.of(context).lbl_dash,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                color: Theme.of(context).customColors.darkGreytextcolor,
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageUpload(BuildContext context, ProfileState state) {
    return Center(
      child: Stack(
        alignment: Alignment.center,
        children: [
          CustomImageView(
            height: 130.h,
            width: 130.h,
            imagePath: Assets.images.svgs.other.icProfileFrame.path,
          ),
          Align(
            alignment: Alignment.center,
            child: Stack(
              children: [
                CustomImageView(
                  onTap: () {
                    context.read<ProfileBloc>().add(SelectUserProfile());
                  },
                  height: 100.h,
                  width: 100.h,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(100.r),
                  imagePath:
                      state.userProfile != null &&
                          state.userProfile!.path.isNotEmpty
                      ? state.userProfile!.path
                      : ApiEndPoint.getImageUrl + state.profileImagePath,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: CustomImageView(
                    onTap: () {
                      context.read<ProfileBloc>().add(SelectUserProfile());
                    },
                    imagePath: Assets.images.pngs.icons.icCamera.path,
                    height: 28.h,
                    width: 28.w,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullNameField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Full Name',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(4.h),
          CustomTextInputField(
            context: context,
            type: InputType.text,
            isCapitalized: true,
            hintLabel: 'Enter your full name',
            controller: state.nameController,
            textInputAction: TextInputAction.next,
            prefixIcon: CustomImageView(
              margin: EdgeInsets.all(15.0.r),
              imagePath: Assets.images.svgs.icons.icProfile.path,
            ),
            onChanged: (value) =>
                context.read<ProfileBloc>().add(NameChanged(value)),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderAndAgeRow(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () async {
                final selectedGender = await showDialog<String>(
                  context: context,
                  builder: (context) => GenderPickerDialog(
                    initialGender: state.selectedGender ?? '',
                  ),
                );

                if (selectedGender != null &&
                    selectedGender != state.selectedGender &&
                    context.mounted) {
                  context.read<ProfileBloc>().add(
                    GenderChanged(selectedGender),
                  );
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.fillColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).customColors.primaryColor!.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: CustomImageView(
                            height: 24.h,
                            imagePath: Assets.images.svgs.icons.icMale.path,
                            color: Theme.of(context).customColors.primaryColor,
                          ),
                        ),
                      ),
                      buildSizedboxW(16.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Lang.of(context).lbl_gender,
                            style: Theme.of(context).textTheme.bodySmall!
                                .copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.darkGreytextcolor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14.sp,
                                ),
                          ),
                          buildSizedBoxH(4.h),
                          Text(
                            state.selectedGender ?? Lang.of(context).lbl_dash,
                            style: Theme.of(context).textTheme.bodySmall!
                                .copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.blackColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          buildSizedboxW(16.w),
          Expanded(
            child: GestureDetector(
              onTap: () async {
                FocusScope.of(context).requestFocus(FocusNode());

                // Parse current DOB if available, otherwise use a default date
                DateTime? initialDate;
                if (state.dobController?.text.isNotEmpty == true) {
                  try {
                    final parts = state.dobController!.text.split('/');
                    if (parts.length == 3) {
                      initialDate = DateTime(
                        int.parse(parts[2]), // year
                        int.parse(parts[1]), // month
                        int.parse(parts[0]), // day
                      );
                    }
                  } catch (e) {
                    // If parsing fails, use default
                  }
                }

                final selectedDate = await showDialog<DateTime>(
                  context: context,
                  builder: (context) =>
                      BirthdayPickerDialog(initialDate: initialDate),
                );

                if (selectedDate != null && context.mounted) {
                  context.read<ProfileBloc>().add(DobChanged(selectedDate));
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).customColors.fillColor,
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(
                            context,
                          ).customColors.primaryColor!.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: CustomImageView(
                            height: 24.h,
                            imagePath: Assets.images.svgs.icons.icPerson.path,
                            color: Theme.of(context).customColors.primaryColor,
                          ),
                        ),
                      ),
                      buildSizedboxW(16.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            Lang.of(context).lbl_age,
                            style: Theme.of(context).textTheme.bodySmall!
                                .copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.darkGreytextcolor,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14.sp,
                                ),
                          ),
                          buildSizedBoxH(4.h),
                          Text(
                            state.ageController.text.isNotEmpty
                                ? state.ageController.text
                                : Lang.of(context).lbl_dash,
                            style: Theme.of(context).textTheme.bodySmall!
                                .copyWith(
                                  color: Theme.of(
                                    context,
                                  ).customColors.blackColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16.sp,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Widget _buildDateOfBirthField(BuildContext context, ProfileState state) {
  //   return Padding(
  //     padding: const EdgeInsets.symmetric(horizontal: 16.0),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           'Date of Birth',
  //           style: Theme.of(context).textTheme.labelLarge?.copyWith(
  //             fontSize: 16.sp,
  //             color: Theme.of(context).customColors.blackColor,
  //           ),
  //         ),
  //         buildSizedBoxH(4.h),
  //         CustomTextInputField(
  //           context: context,
  //           type: InputType.text,
  //           readOnly: true,
  //           hintLabel: "DD/MM/YYYY",
  //           controller: state.dobController,
  //           textInputAction: TextInputAction.next,
  //           prefixIcon: CustomImageView(
  //             margin: EdgeInsets.all(15.0.r),
  //             imagePath: Assets.images.svgs.icons.icCalender.path,
  //           ),
  //           onTap: () async {
  //             FocusScope.of(context).requestFocus(FocusNode());

  //             // Parse current DOB if available, otherwise use a default date
  //             DateTime? initialDate;
  //             if (state.dobController?.text.isNotEmpty == true) {
  //               try {
  //                 final parts = state.dobController!.text.split('/');
  //                 if (parts.length == 3) {
  //                   initialDate = DateTime(
  //                     int.parse(parts[2]), // year
  //                     int.parse(parts[1]), // month
  //                     int.parse(parts[0]), // day
  //                   );
  //                 }
  //               } catch (e) {
  //                 // If parsing fails, use default
  //               }
  //             }

  //             final selectedDate = await showDialog<DateTime>(
  //               context: context,
  //               builder: (context) =>
  //                   BirthdayPickerDialog(initialDate: initialDate),
  //             );

  //             if (selectedDate != null && context.mounted) {
  //               context.read<ProfileBloc>().add(DobChanged(selectedDate));
  //             }
  //           },
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildPreferredGenderField(BuildContext context, ProfileState state) {
    final genderOptions = genderIdMap.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preferred Gender',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: genderOptions,
            selectedOption: state.selectedPreferredGender,
            onSelected: (value) {
              context.read<ProfileBloc>().add(PreferredGenderChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSmokingPreferenceField(
    BuildContext context,
    ProfileState state,
  ) {
    final smokingOptions = smokingIdMap.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Smoking Preference',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: smokingOptions,
            selectedOption: state.selectedSmokingPerson,
            onSelected: (value) {
              context.read<ProfileBloc>().add(SmokingPreferenceChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCleanlinessField(BuildContext context, ProfileState state) {
    final cleanlinessOptions = cleanlinessIdMap.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cleanliness Level',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: cleanlinessOptions,
            selectedOption: state.sleectedCleanLevenl,
            onSelected: (value) {
              context.read<ProfileBloc>().add(CleanlinessChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPetPreferenceField(BuildContext context, ProfileState state) {
    final petOptions = petBoolMap.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Having Pet',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: petOptions,
            selectedOption: state.selectedPet,
            onSelected: (value) {
              context.read<ProfileBloc>().add(PetPreferenceChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildClassStandingField(BuildContext context, ProfileState state) {
    final classOptions = classStandingIdMap.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Class Standing',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: classOptions,
            selectedOption: state.selectedClassStand,
            onSelected: (value) {
              context.read<ProfileBloc>().add(ClassStandingChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalityContainer(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Theme.of(context).customColors.fillColor,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Personality',
                    style: Theme.of(context).textTheme.labelLarge?.copyWith(
                      fontSize: 16.sp,
                      color: Theme.of(context).customColors.blackColor,
                    ),
                  ),
                  GestureDetector(
                    onTap: () async {
                      final controller = TextEditingController();
                      final result = await showDialog<String>(
                        context: context,
                        builder: (context) => Dialog(
                          insetPadding: EdgeInsets.symmetric(horizontal: 40.w),
                          child: Container(
                            padding: EdgeInsets.all(20.w),
                            decoration: BoxDecoration(
                              color: Theme.of(
                                context,
                              ).customColors.scaffoldColor,
                              borderRadius: BorderRadius.circular(16.r),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Add Personality Tag',
                                  style: Theme.of(context).textTheme.bodyLarge
                                      ?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Theme.of(
                                          context,
                                        ).customColors.blackColor,
                                        fontSize: 18.sp,
                                      ),
                                ),
                                buildSizedBoxH(16.h),
                                CustomTextInputField(
                                  controller: controller,
                                  hintLabel: 'Enter tag',
                                  context: context,
                                  type: InputType.text,
                                  isCapitalized: true,
                                ),
                                buildSizedBoxH(20.h),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: Text(
                                        'Cancel',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(
                                              color: Theme.of(
                                                context,
                                              ).customColors.darkGreytextcolor,
                                            ),
                                      ),
                                    ),
                                    buildSizedboxW(8.w),
                                    CustomElevatedButton(
                                      text: 'Add',
                                      height: 36.h,
                                      width: 80.w,
                                      onPressed: () => Navigator.pop(
                                        context,
                                        controller.text,
                                      ),
                                      buttonTextStyle: Theme.of(context)
                                          .textTheme
                                          .bodySmall
                                          ?.copyWith(
                                            color: Theme.of(
                                              context,
                                            ).customColors.fillColor,
                                            fontWeight: FontWeight.w600,
                                          ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      );

                      if (result != null &&
                          result.trim().isNotEmpty &&
                          context.mounted) {
                        final trimmedResult = result.trim();
                        context.read<ProfileBloc>().add(
                          AddCustomPersonalityTag(trimmedResult),
                        );
                        context.read<ProfileBloc>().add(
                          AddPersonalityTag(trimmedResult),
                        );
                      }
                    },
                    child: SizedBox(
                      height: 22.h,
                      width: 22.w,
                      child: Icon(Icons.add, size: 18),
                    ),
                  ),
                ],
              ),
              buildSizedBoxH(8.h),
              _buildEditableUserTags(context, state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEditableUserTags(BuildContext context, ProfileState state) {
    final List<String> predefinedTags = [
      'Friendly',
      'Organized',
      'Quiet',
      'Outgoing',
      'Clean',
      'Adventurous',
    ];

    // Combine predefined and custom tags, avoiding duplicates
    final List<String> allTags = [
      ...predefinedTags,
      ...state.customPersonalityTags.where(
        (tag) => !predefinedTags.contains(tag),
      ),
    ];

    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: [
        ...allTags.map((tag) {
          final isSelected = state.personalityTags.contains(tag);
          return GestureDetector(
            onTap: () {
              if (isSelected) {
                context.read<ProfileBloc>().add(RemovePersonalityTag(tag));
              } else {
                context.read<ProfileBloc>().add(AddPersonalityTag(tag));
              }
            },
            child: CustomGradientContainer(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              topColor: isSelected
                  ? Theme.of(context).customColors.primaryColor!
                  : Theme.of(context).customColors.lightGreyTextColor!,
              bottomColor: isSelected
                  ? Theme.of(context).customColors.primaryColor!
                  : Theme.of(context).customColors.lightGreyTextColor!,
              fillColor: Colors.transparent,
              height: 32.h,
              child: Text(
                tag,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                  color: isSelected
                      ? Theme.of(context).customColors.primaryColor
                      : Theme.of(context).customColors.darkGreytextcolor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildHabitsLifestyleField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Habits & Lifestyle',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          _buildMultiSelectChips(
            context: context,
            options: state.habitsLifestyle,
            selectedIds: state.selectedHabitsAndLifestyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectHabitsAndLifestyle(habitsAndLifestyle: name),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLivingStyleField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Living Style',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          _buildMultiSelectChips(
            context: context,
            options: state.livingStyle,
            selectedIds: state.selectedCleanlinessLivingStyle,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectCleanlinessLivingStyle(cleanlinessLivingStyle: name),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInterestsHobbiesField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Interests & Hobbies',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          _buildMultiSelectChips(
            context: context,
            options: state.interestsHobbies,
            selectedIds: state.selectedInterestsHobbies,
            onSelect: (name) {
              context.read<ProfileBloc>().add(
                SelectInterestsHobbies(interestsHobbies: name),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContactNumberField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Contact Number',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(4.h),
          CustomTextInputField(
            context: context,
            type: InputType.phoneNumber,
            hintLabel: 'Enter contact number',
            controller: state.contactNumberController,
            textInputAction: TextInputAction.next,
            prefixIcon: CustomImageView(
              margin: EdgeInsets.all(15.0.r),
              imagePath: Assets.images.svgs.icons.icPhone.path,
            ),
            onChanged: (value) =>
                context.read<ProfileBloc>().add(ContactNumberChanged(value)),
          ),
        ],
      ),
    );
  }

  Widget _buildLeasePeriodField(BuildContext context, ProfileState state) {
    final leaseOptions = leasePeriodDays.keys.toList();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Lease Period',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(10.h),
          buildSelectableChipsRow(
            context: context,
            options: leaseOptions,
            selectedOption: state.selectedPeriod,
            onSelected: (value) {
              context.read<ProfileBloc>().add(LeasePeriodChanged(value));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAboutField(BuildContext context, ProfileState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(4.h),
          CustomTextInputField(
            context: context,
            controller: state.aboutController,
            hintLabel: 'Tell us about yourself...',
            type: InputType.text,
            maxLines: 4,
            borderRadius: 20.r,
            onChanged: (value) =>
                context.read<ProfileBloc>().add(AboutChanged(value)),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferredLocationsField(
    BuildContext context,
    ProfileState state,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Preferred Locations',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
          buildSizedBoxH(4.h),

          // Add location button
          GestureDetector(
            onTap: () => _showLocationSearchBottomSheet(context),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Theme.of(context).customColors.primaryColor!,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.add_location_alt,
                    color: Theme.of(context).customColors.primaryColor,
                    size: 20,
                  ),
                  buildSizedboxW(8.w),
                  Text(
                    'Add Preferred Location',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).customColors.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Selected locations
          if (state.selectedLocations.isNotEmpty) ...[
            buildSizedBoxH(12.h),
            ...state.selectedLocations.map(
              (location) => Padding(
                padding: EdgeInsets.only(bottom: 8.h),
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).customColors.fillColor,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: Theme.of(
                        context,
                      ).customColors.lightgreycolor!.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Theme.of(context).customColors.primaryColor,
                        size: 16,
                      ),
                      buildSizedboxW(8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              location.name,
                              style: Theme.of(context).textTheme.bodyMedium
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            if (location.address != null)
                              Text(
                                location.address!,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(
                                      color: Theme.of(
                                        context,
                                      ).customColors.lightgreycolor,
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () => context.read<ProfileBloc>().add(
                          RemoveLocation(location),
                        ),
                        child: Icon(
                          Icons.close,
                          color: Theme.of(context).customColors.redcolor,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _showLocationSearchBottomSheet(BuildContext context) async {
    // Request location permission before showing the bottom sheet
    final status = await Permission.location.request();
    if (status.isGranted && context.mounted) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => const LocationSearchBottomSheet(),
      );
    } else if (status.isDenied || status.isPermanentlyDenied) {
      // Show a message if permission is denied
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Location permission is required to search for locations.',
            ),
            backgroundColor: Theme.of(context).customColors.redcolor,
          ),
        );
      }
    }
  }

  Widget _buildUploadMultiplePhotos(BuildContext context, ProfileState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            'Photo Upload',
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.blackColor,
            ),
          ),
        ),
        buildSizedBoxH(20.h),
        SizedBox(
          height: 90.h,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: state.photoPaths.length + 1,
            separatorBuilder: (_, __) => SizedBox(width: 8.w),
            padding: const EdgeInsets.only(right: 16.0),
            itemBuilder: (context, index) {
              if (index == 0) {
                // Add-photo button
                return Padding(
                  padding: const EdgeInsets.only(left: 16.0),
                  child: GestureDetector(
                    onTap: () async {
                      final images =
                          await Imagepickerutils.pickMultipleImagesFromGallery();
                      if (images.isNotEmpty && context.mounted) {
                        final imagePaths = images
                            .map((img) => img.path)
                            .toList();
                        context.read<ProfileBloc>().add(
                          SelectMultiplePhotos(imagePaths),
                        );
                      }
                    },
                    child: Container(
                      height: 90.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        color: Theme.of(context).customColors.fillColor,
                        borderRadius: BorderRadius.circular(16.r),
                        border: Border.all(
                          color: Theme.of(context).customColors.primaryColor!,
                          width: 1.5,
                        ),
                      ),
                      child: Icon(
                        Icons.add_a_photo,
                        size: 32,
                        color: Theme.of(context).customColors.primaryColor,
                      ),
                    ),
                  ),
                );
              } else {
                final imagePath = state.photoPaths[index - 1];
                return Stack(
                  children: [
                    CustomImageView(
                      height: 90.h,
                      width: 80.w,
                      fit: BoxFit.cover,
                      imagePath: ApiEndPoint.getImageUrl + imagePath,
                      radius: BorderRadius.circular(12.r),
                    ),
                    Positioned(
                      top: 2,
                      right: 2,
                      child: GestureDetector(
                        onTap: () {
                          context.read<ProfileBloc>().add(
                            RemovePhoto(imagePath),
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).customColors.fillColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.close,
                            size: 18,
                            color: Theme.of(context).customColors.redcolor,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),
      ],
    );
  }

  Widget buildSelectableChipsRow({
    required BuildContext context,
    required List<String> options,
    required String? selectedOption,
    required ValueChanged<String> onSelected,
  }) {
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesign(
            context: context,
            text: options[index],
            isSelected: options[index] == selectedOption,
            onTap: () => onSelected(options[index]),
          ),
        ),
      ),
    );
  }

  Widget _buildOptionDesign({
    required BuildContext context,
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Chip(
        label: Text(
          text,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: isSelected
                ? Theme.of(context).customColors.fillColor
                : Theme.of(context).customColors.darkGreytextcolor,
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isSelected
            ? Theme.of(context).customColors.primaryColor
            : Theme.of(context).customColors.fillColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r),
          side: BorderSide(color: Colors.transparent, width: 0),
        ),
      ),
    );
  }

  Widget _buildMultiSelectChips({
    required BuildContext context,
    required List<ProfileOptionModel> options,
    required List<int> selectedIds,
    required void Function(String name) onSelect,
  }) {
    return Wrap(
      children: List.generate(
        options.length,
        (index) => Padding(
          padding: EdgeInsets.only(right: 5.w),
          child: _buildOptionDesignWithIcon(
            context: context,
            text: options[index].name ?? '',
            icon: options[index].icon ?? '',
            isSelected: selectedIds.contains(options[index].id),
            onTap: () => onSelect(options[index].name ?? ''),
          ),
        ),
      ),
    );
  }

  Widget _buildOptionDesignWithIcon({
    required BuildContext context,
    required String text,
    required String icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Chip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon.isNotEmpty) ...[
              CustomImageView(
                imagePath: ApiEndPoint.getImageUrl + icon,
                height: 20.h,
                width: 20.w,
              ),
              buildSizedboxW(5.w),
            ],
            Text(
              text,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isSelected
                    ? Theme.of(context).customColors.fillColor
                    : Theme.of(context).customColors.darkGreytextcolor,
                fontSize: 16.0.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        backgroundColor: isSelected
            ? Theme.of(context).customColors.primaryColor
            : Theme.of(context).customColors.fillColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30.r),
          side: BorderSide(color: Colors.transparent, width: 0),
        ),
      ),
    );
  }
}
