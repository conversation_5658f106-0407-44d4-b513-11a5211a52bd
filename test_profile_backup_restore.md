# Profile Edit Backup/Restore Test

## Test Scenario
This test verifies that profile data is only updated in the UI after a successful save operation.

## Steps to Test

### Test 1: Edit and Go Back Without Saving
1. Open the app and navigate to Profile screen
2. Note the current profile data (name, gender, age, etc.)
3. Tap the Edit button to go to Edit Profile screen
4. Make changes to various fields:
   - Change the name
   - Select a different gender
   - Change contact number
   - Select different personality tags
   - Add/remove photos
   - Select a new profile image
5. **DO NOT** tap the Save button
6. Use the back button or gesture to go back to Profile screen
7. **Expected Result**: All data should be reverted to the original state
   - Name should be the original name
   - Gender should be the original gender
   - Contact number should be the original number
   - Personality tags should be the original selection
   - Photos should be the original photos
   - Profile image should be the original image

### Test 2: Edit, Save, and Verify Persistence
1. Open Edit Profile screen again
2. Make the same changes as in Test 1
3. Tap the **Save** button
4. Wait for the save operation to complete
5. Go back to Profile screen
6. **Expected Result**: All changes should be persisted
   - All modified data should be visible
   - Changes should remain even after going back

### Test 3: Edit After Save, Then Cancel
1. From the Profile screen (with saved changes), tap Edit again
2. Make additional changes to the data
3. **DO NOT** save
4. Go back to Profile screen
5. **Expected Result**: Should show the previously saved data, not the new unsaved changes

## Technical Implementation

### Key Components Fixed:
1. **EditProfileScreen**: Removed premature backup update on save button press
2. **ProfileBloc**: Added backup update only after successful save
3. **ProfileState**: Added backup field for userProfile (selected image)
4. **Backup/Restore**: Enhanced to handle all profile fields including images

### Flow:
```
Enter Edit Mode → Backup Current State
↓
Make Changes → Update UI Only
↓
Save Button → API Call → Success? → Update Backup → Navigate Back
                      ↓
                    Failure → Keep Original Backup
↓
Back Button → Restore from Backup → Navigate Back
```

This ensures data integrity and proper user experience where changes are only persisted after explicit save operations.
